import javax.microedition.lcdui.Canvas;
import javax.microedition.lcdui.Command;
import javax.microedition.lcdui.CommandListener;
import javax.microedition.lcdui.Displayable;
import javax.microedition.lcdui.Graphics;

// Mock classes for testing
class MockNavigator {
    public void back() {}
    public void showAlert(String msg, int type) {}
}

class MockLang {
    public static String tr(String key) {
        if ("action.ok".equals(key)) return "OK";
        if ("action.cancel".equals(key)) return "Cancel";
        return key;
    }
}

public final class ColorPickerTest extends Canvas implements CommandListener {
    private final MockNavigator navigator;
    private final ColorPickerListener listener;
    private final Command selectCommand;
    private final Command cancelCommand;
    
    private static final int GRID_SIZE = 16;
    private static final int CELL_SIZE = 12;
    private static final int PADDING = 5;
    
    private int selectedX = 0;
    private int selectedY = 0;
    private int gridStartX;
    private int gridStartY;
    
    private static final int[] COLORS = {
        0x000000, 0x800000, 0x008000, 0x808000, 0x000080, 0x800080, 0x008080, 0xC0C0C0,
        0x808080, 0xFF0000, 0x00FF00, 0xFFFF00, 0x0000FF, 0xFF00FF, 0x00FFFF, 0xFFFFFF,
        0x000000, 0x00005F, 0x000087, 0x0000AF, 0x0000D7, 0x0000FF, 0x005F00, 0x005F5F,
        0x005F87, 0x005FAF, 0x005FD7, 0x005FFF, 0x008700, 0x00875F, 0x008787, 0x0087AF,
        0x0087D7, 0x0087FF, 0x00AF00, 0x00AF5F, 0x00AF87, 0x00AFAF, 0x00AFD7, 0x00AFFF,
        0x00D700, 0x00D75F, 0x00D787, 0x00D7AF, 0x00D7D7, 0x00D7FF, 0x00FF00, 0x00FF5F,
        0x00FF87, 0x00FFAF, 0x00FFD7, 0x00FFFF, 0x5F0000, 0x5F005F, 0x5F0087, 0x5F00AF,
        0x5F00D7, 0x5F00FF, 0x5F5F00, 0x5F5F5F, 0x5F5F87, 0x5F5FAF, 0x5F5FD7, 0x5F5FFF,
        0x5F8700, 0x5F875F, 0x5F8787, 0x5F87AF, 0x5F87D7, 0x5F87FF, 0x5FAF00, 0x5FAF5F,
        0x5FAF87, 0x5FAFAF, 0x5FAFD7, 0x5FAFFF, 0x5FD700, 0x5FD75F, 0x5FD787, 0x5FD7AF,
        0x5FD7D7, 0x5FD7FF, 0x5FFF00, 0x5FFF5F, 0x5FFF87, 0x5FFFAF, 0x5FFFD7, 0x5FFFFF,
        0x870000, 0x87005F, 0x870087, 0x8700AF, 0x8700D7, 0x8700FF, 0x875F00, 0x875F5F,
        0x875F87, 0x875FAF, 0x875FD7, 0x875FFF, 0x878700, 0x87875F, 0x878787, 0x8787AF,
        0x8787D7, 0x8787FF, 0x87AF00, 0x87AF5F, 0x87AF87, 0x87AFAF, 0x87AFD7, 0x87AFFF,
        0x87D700, 0x87D75F, 0x87D787, 0x87D7AF, 0x87D7D7, 0x87D7FF, 0x87FF00, 0x87FF5F,
        0x87FF87, 0x87FFAF, 0x87FFD7, 0x87FFFF, 0xAF0000, 0xAF005F, 0xAF0087, 0xAF00AF,
        0xAF00D7, 0xAF00FF, 0xAF5F00, 0xAF5F5F, 0xAF5F87, 0xAF5FAF, 0xAF5FD7, 0xAF5FFF,
        0xAF8700, 0xAF875F, 0xAF8787, 0xAF87AF, 0xAF87D7, 0xAF87FF, 0xAFAF00, 0xAFAF5F,
        0xAFAF87, 0xAFAFAF, 0xAFAFD7, 0xAFAFFF, 0xAFD700, 0xAFD75F, 0xAFD787, 0xAFD7AF,
        0xAFD7D7, 0xAFD7FF, 0xAFFF00, 0xAFFF5F, 0xAFFF87, 0xAFFFAF, 0xAFFFD7, 0xAFFFFF,
        0xD70000, 0xD7005F, 0xD70087, 0xD700AF, 0xD700D7, 0xD700FF, 0xD75F00, 0xD75F5F,
        0xD75F87, 0xD75FAF, 0xD75FD7, 0xD75FFF, 0xD78700, 0xD7875F, 0xD78787, 0xD787AF,
        0xD787D7, 0xD787FF, 0xD7AF00, 0xD7AF5F, 0xD7AF87, 0xD7AFAF, 0xD7AFD7, 0xD7AFFF,
        0xD7D700, 0xD7D75F, 0xD7D787, 0xD7D7AF, 0xD7D7D7, 0xD7D7FF, 0xD7FF00, 0xD7FF5F,
        0xD7FF87, 0xD7FFAF, 0xD7FFD7, 0xD7FFFF, 0xFF0000, 0xFF005F, 0xFF0087, 0xFF00AF,
        0xFF00D7, 0xFF00FF, 0xFF5F00, 0xFF5F5F, 0xFF5F87, 0xFF5FAF, 0xFF5FD7, 0xFF5FFF,
        0xFF8700, 0xFF875F, 0xFF8787, 0xFF87AF, 0xFF87D7, 0xFF87FF, 0xFFAF00, 0xFFAF5F,
        0xFFAF87, 0xFFAFAF, 0xFFAFD7, 0xFFAFFF, 0xFFD700, 0xFFD75F, 0xFFD787, 0xFFD7AF,
        0xFFD7D7, 0xFFD7FF, 0xFFFF00, 0xFFFF5F, 0xFFFF87, 0xFFFFAF, 0xFFFFD7, 0xFFFFFF,
        0x080808, 0x121212, 0x1C1C1C, 0x262626, 0x303030, 0x3A3A3A, 0x444444, 0x4E4E4E,
        0x585858, 0x626262, 0x6C6C6C, 0x767676, 0x808080, 0x8A8A8A, 0x949494, 0x9E9E9E,
        0xA8A8A8, 0xB2B2B2, 0xBCBCBC, 0xC6C6C6, 0xD0D0D0, 0xDADADA, 0xE4E4E4, 0xEEEEEE
    };
    
    public ColorPickerTest(MockNavigator navigator, ColorPickerListener listener) {
        this.navigator = navigator;
        this.listener = listener;
        
        this.selectCommand = new Command(MockLang.tr("action.ok"), Command.OK, 0);
        this.cancelCommand = new Command(MockLang.tr("action.cancel"), Command.CANCEL, 1);
        
        addCommand(selectCommand);
        addCommand(cancelCommand);
        setCommandListener(this);
        
        calculateGridPosition();
    }
    
    private void calculateGridPosition() {
        int screenWidth = getWidth();
        int screenHeight = getHeight();
        int gridWidth = GRID_SIZE * CELL_SIZE;
        int gridHeight = GRID_SIZE * CELL_SIZE;
        
        gridStartX = (screenWidth - gridWidth) / 2;
        gridStartY = (screenHeight - gridHeight) / 2;
        
        if (gridStartX < PADDING) gridStartX = PADDING;
        if (gridStartY < PADDING) gridStartY = PADDING;
    }
    
    protected void paint(Graphics g) {
        g.setColor(0xFFFFFF);
        g.fillRect(0, 0, getWidth(), getHeight());
        
        calculateGridPosition();
        
        for (int y = 0; y < GRID_SIZE; y++) {
            for (int x = 0; x < GRID_SIZE; x++) {
                int colorIndex = y * GRID_SIZE + x;
                if (colorIndex < COLORS.length) {
                    int color = COLORS[colorIndex];
                    g.setColor(color);
                    
                    int cellX = gridStartX + x * CELL_SIZE;
                    int cellY = gridStartY + y * CELL_SIZE;
                    
                    g.fillRect(cellX, cellY, CELL_SIZE - 1, CELL_SIZE - 1);
                    
                    if (x == selectedX && y == selectedY) {
                        g.setColor(0x000000);
                        g.drawRect(cellX - 1, cellY - 1, CELL_SIZE, CELL_SIZE);
                        g.setColor(0xFFFFFF);
                        g.drawRect(cellX - 2, cellY - 2, CELL_SIZE + 2, CELL_SIZE + 2);
                    }
                }
            }
        }
        
        int selectedColor = getSelectedColor();
        g.setColor(0x000000);
        g.drawString("Selected: #" + Integer.toHexString(selectedColor).toUpperCase(), 
                    5, getHeight() - 20, Graphics.LEFT | Graphics.BOTTOM);
    }
    
    protected void keyPressed(int keyCode) {
        int gameAction = getGameAction(keyCode);
        
        switch (gameAction) {
            case UP:
                if (selectedY > 0) {
                    selectedY--;
                    repaint();
                }
                break;
            case DOWN:
                if (selectedY < GRID_SIZE - 1) {
                    selectedY++;
                    repaint();
                }
                break;
            case LEFT:
                if (selectedX > 0) {
                    selectedX--;
                    repaint();
                }
                break;
            case RIGHT:
                if (selectedX < GRID_SIZE - 1) {
                    selectedX++;
                    repaint();
                }
                break;
            case FIRE:
                selectCurrentColor();
                break;
        }
    }
    
    protected void pointerPressed(int x, int y) {
        int gridX = (x - gridStartX) / CELL_SIZE;
        int gridY = (y - gridStartY) / CELL_SIZE;
        
        if (gridX >= 0 && gridX < GRID_SIZE && gridY >= 0 && gridY < GRID_SIZE) {
            selectedX = gridX;
            selectedY = gridY;
            repaint();
        }
    }
    
    protected void pointerReleased(int x, int y) {
        int gridX = (x - gridStartX) / CELL_SIZE;
        int gridY = (y - gridStartY) / CELL_SIZE;
        
        if (gridX >= 0 && gridX < GRID_SIZE && gridY >= 0 && gridY < GRID_SIZE) {
            if (gridX == selectedX && gridY == selectedY) {
                selectCurrentColor();
            }
        }
    }
    
    private void selectCurrentColor() {
        int selectedColor = getSelectedColor();
        listener.onColorSelected(selectedColor);
        navigator.back();
    }
    
    private int getSelectedColor() {
        int colorIndex = selectedY * GRID_SIZE + selectedX;
        return colorIndex < COLORS.length ? COLORS[colorIndex] : 0x000000;
    }
    
    public void commandAction(Command c, Displayable d) {
        if (c == selectCommand) {
            selectCurrentColor();
        } else if (c == cancelCommand) {
            navigator.back();
        }
    }
    
    public interface ColorPickerListener {
        void onColorSelected(int color);
    }
}
