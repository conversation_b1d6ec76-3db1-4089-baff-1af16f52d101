import javax.microedition.lcdui.Command;
import javax.microedition.lcdui.Form;
import javax.microedition.lcdui.Item;
import javax.microedition.lcdui.ItemCommandListener;
import javax.microedition.lcdui.StringItem;

public class ButtonTest extends Form implements ItemCommandListener {
    private StringItem colorPickerItem;
    
    public ButtonTest() {
        super("Test");
        
        // Tạo button với Item.BUTTON appearance
        colorPickerItem = new StringItem(null, "Color Picker", Item.BUTTON);
        Command selectCommand = new Command("Color Picker", Command.ITEM, 1);
        colorPickerItem.addCommand(selectCommand);
        colorPickerItem.setItemCommandListener(this);
        colorPickerItem.setDefaultCommand(selectCommand);
        
        this.append(colorPickerItem);
    }
    
    public void commandAction(Command c, Item item) {
        if (item == colorPickerItem) {
            System.out.println("Button clicked!");
        }
    }
}
