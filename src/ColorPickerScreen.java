import javax.microedition.lcdui.Canvas;
import javax.microedition.lcdui.Command;
import javax.microedition.lcdui.CommandListener;
import javax.microedition.lcdui.Displayable;
import javax.microedition.lcdui.Graphics;

public final class ColorPickerScreen extends Canvas implements CommandListener {
  private final Navigator navigator;
  private final ColorPickerListener listener;
  private final Command selectCommand;
  private final Command cancelCommand;

  private static final int GRID_SIZE = 8;
  private static final int CELL_SIZE = 32;
  private static final int PADDING = 2;

  private int selectedX = 0;
  private int selectedY = 0;
  private int gridStartX;
  private int gridStartY;

  private static final int[] COLORS = {
    0x000000, 0x800000, 0x008000, 0x808000, 0x000080, 0x800080, 0x008080, 0xC0C0C0,
    0x808080, 0xFF0000, 0x00FF00, 0xFFFF00, 0x0000FF, 0xFF00FF, 0x00FFFF, 0xFFFFFF,
    0x000000, 0x00005F, 0x000087, 0x0000AF,
  };

  public ColorPickerScreen(Navigator navigator, ColorPickerListener listener) {
    this.navigator = navigator;
    this.listener = listener;

    this.selectCommand = new Command(Lang.tr("action.ok"), Command.OK, 0);
    this.cancelCommand = new Command(Lang.tr("action.cancel"), Command.CANCEL, 1);

    addCommand(selectCommand);
    addCommand(cancelCommand);
    setCommandListener(this);

    calculateGridPosition();
  }

  private void calculateGridPosition() {
    int screenWidth = getWidth();
    int screenHeight = getHeight();
    int gridWidth = GRID_SIZE * CELL_SIZE;
    int gridHeight = GRID_SIZE * CELL_SIZE;

    gridStartX = (screenWidth - gridWidth) / 2;
    gridStartY = (screenHeight - gridHeight) / 2;

    if (gridStartX < PADDING) gridStartX = PADDING;
    if (gridStartY < PADDING) gridStartY = PADDING;
  }

  protected void paint(Graphics g) {
    g.setColor(0xFFFFFF);
    g.fillRect(0, 0, getWidth(), getHeight());

    calculateGridPosition();

    for (int y = 0; y < GRID_SIZE; y++) {
      for (int x = 0; x < GRID_SIZE; x++) {
        int colorIndex = y * GRID_SIZE + x;
        if (colorIndex < COLORS.length) {
          int color = COLORS[colorIndex];
          g.setColor(color);

          int cellX = gridStartX + x * CELL_SIZE;
          int cellY = gridStartY + y * CELL_SIZE;

          g.fillRect(cellX, cellY, CELL_SIZE - 1, CELL_SIZE - 1);

          if (x == selectedX && y == selectedY) {
            g.setColor(0x000000);
            g.drawRect(cellX - 1, cellY - 1, CELL_SIZE, CELL_SIZE);
            g.setColor(0xFFFFFF);
            g.drawRect(cellX - 2, cellY - 2, CELL_SIZE + 2, CELL_SIZE + 2);
          }
        }
      }
    }

    int selectedColor = getSelectedColor();
    g.setColor(0x000000);
    g.drawString(
        "Selected: #" + Integer.toHexString(selectedColor).toUpperCase(),
        5,
        getHeight() - 20,
        Graphics.LEFT | Graphics.BOTTOM);
  }

  protected void keyPressed(int keyCode) {
    int gameAction = getGameAction(keyCode);

    switch (gameAction) {
      case UP:
        if (isValidPosition(selectedX, selectedY - 1)) {
          selectedY--;
          repaint();
        }
        break;
      case DOWN:
        if (isValidPosition(selectedX, selectedY + 1)) {
          selectedY++;
          repaint();
        }
        break;
      case LEFT:
        if (isValidPosition(selectedX - 1, selectedY)) {
          selectedX--;
          repaint();
        }
        break;
      case RIGHT:
        if (isValidPosition(selectedX + 1, selectedY)) {
          selectedX++;
          repaint();
        }
        break;
      case FIRE:
        selectCurrentColor();
        break;
    }
  }

  protected void pointerPressed(int x, int y) {
    int gridX = (x - gridStartX) / CELL_SIZE;
    int gridY = (y - gridStartY) / CELL_SIZE;

    if (isValidPosition(gridX, gridY)) {
      selectedX = gridX;
      selectedY = gridY;
      repaint();
    }
  }

  protected void pointerReleased(int x, int y) {
    int gridX = (x - gridStartX) / CELL_SIZE;
    int gridY = (y - gridStartY) / CELL_SIZE;

    if (isValidPosition(gridX, gridY) && gridX == selectedX && gridY == selectedY) {
      selectCurrentColor();
    }
  }

  private void selectCurrentColor() {
    int selectedColor = getSelectedColor();
    listener.onColorSelected(selectedColor);
    navigator.back();
  }

  private boolean isValidPosition(int x, int y) {
    if (x < 0 || x >= GRID_SIZE || y < 0 || y >= GRID_SIZE) {
      return false;
    }
    int colorIndex = y * GRID_SIZE + x;
    return colorIndex < COLORS.length;
  }

  private int getSelectedColor() {
    int colorIndex = selectedY * GRID_SIZE + selectedX;
    return colorIndex < COLORS.length ? COLORS[colorIndex] : 0x000000;
  }

  public void commandAction(Command c, Displayable d) {
    if (c == selectCommand) {
      selectCurrentColor();
    } else if (c == cancelCommand) {
      navigator.back();
    }
  }

  public interface ColorPickerListener {
    void onColorSelected(int color);
  }
}
